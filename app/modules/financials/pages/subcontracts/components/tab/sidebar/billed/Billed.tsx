import { useEffect, useState } from "react";
import { type RadioChangeEvent } from "antd";
import { useNavigate } from "@remix-run/react";
// Hook, Helper
import { routes } from "~/route-services/routes";
import { handleDecimalInput } from "~/shared/utils/helper/common";
import { useTranslation } from "~/hook";
import { BILLEDOPTION } from "~/modules/financials/pages/subcontracts/utils/constants";
import {
  getSCGenerateBillApi,
  sCGenerateBillApi,
} from "~/modules/financials/pages/subcontracts/redux/action";
// atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { RadioGroupList } from "~/shared/components/molecules/radioGroupList";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

const Billed = ({ isOpen, subContractId, onClose }: IBilledProps) => {
  const { _t } = useTranslation();
  const navigate = useNavigate();
  const { formatter } = useCurrencyFormatter();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isFormLoading, setIsFormLoading] = useState<boolean>(false);
  const [radioVal, setRadioVal] = useState<string>("maximum");
  const [generateBills, setGenerateBills] = useState<ISCGenerateBillItem[]>([]);
  const [inputValues, setInputValues] = useState<Record<number, string>>({});

  const getGenerateBills = async (id: number) => {
    try {
      setIsLoading(true);
      const res = (await getSCGenerateBillApi({
        id: id?.toString(),
      })) as ISCGetGenerateBillApiRes;

      if (res?.success) {
        const billVal = res?.data?.length > 0 ? res.data : [];
        setGenerateBills(billVal);
      } else {
        notification.error({
          description: res.message,
        });
      }
      setIsLoading(false);
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getGenerateBills(subContractId);
  }, []);

  useEffect(() => {
    setInputValues(
      generateBills.reduce(
        (acc, item) => ({
          ...acc,
          [item?.item_id]:
            radioVal == "fixed"
              ? 0
              : Number(100 - Number(item?.total_sc_paid_bill_percentage)) < 100
              ? Number(
                  100 - Number(item?.total_sc_paid_bill_percentage)
                ).toFixed(2)
              : Number(100 - Number(item?.total_sc_paid_bill_percentage)),
        }),
        {}
      )
    );
  }, [generateBills, radioVal]);

  // Handle input change by updating inputValues state
  const handleInputChange = (id: number, newValue: string, maxVal: number) => {
    setInputValues((prevValues) => ({
      ...prevValues,
      [id]: newValue,
    }));
  };

  const handleFixedIntChange = (e: HTMLInputElement) => {
    const { value } = e;
    setInputValues(
      generateBills.reduce(
        (acc, item) => ({
          ...acc,
          [item?.item_id]:
            Number(value) > 100 - Number(item?.total_sc_paid_bill_percentage)
              ? Number(
                  100 - Number(item?.total_sc_paid_bill_percentage)
                ).toFixed(2)
              : value,
        }),
        {}
      )
    );
  };

  const handleGenerateBill = async () => {
    const billItems: ISCBilledBillItem[] = Object.entries(inputValues).map(
      ([key, value]) => {
        const selBill = generateBills.find(
          (item) => item?.item_id.toString() === key.toString()
        );
        const fAmount = Number(selBill?.total || 0);
        const finalAmount = (fAmount * Number(value)) / 100;

        return {
          item_id: key,
          final_percentage: Number(value || 0),
          final_amount: Math.round(finalAmount),
        };
      }
    );

    const formData: ISCBilledFormParmas = {
      sub_contract_id: subContractId?.toString() || "",
      bill_option: radioVal,
      bill_items: radioVal != "leave_blank" ? billItems : undefined,
    };
    setIsFormLoading(true);
    try {
      const apiRes = (await sCGenerateBillApi(
        formData
      )) as ISCGenerateBillApiRes;
      if (apiRes?.success) {
        const billId = apiRes?.data?.id;
        navigate(`/${routes.BILLS.url}/${billId}`);
      } else {
        notification.error({
          description: apiRes?.message,
        });
        setIsFormLoading(false);
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message,
      });
      setIsFormLoading(false);
    }
  };

  return (
    <Drawer
      open={isOpen}
      rootClassName="drawer-open"
      width={750}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-magnifying-glass-dollar"
            />
          </div>
          <Header
            level={5}
            className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
          >
            {_t("How Much Should Be Billed?")}
          </Header>
        </div>
      }
      closeIcon={<CloseButton onClick={() => onClose()} />}
    >
      <div className="py-4">
        <div className="sidebar-body h-[calc(100vh-132px)]">
          <div className="grid gap-4">
            <div className="px-4 h-[calc(100vh-135px)]">
              <SidebarCardBorder addGap={true}>
                {isLoading ? (
                  <Spin className="w-full h-20 flex items-center justify-center" />
                ) : (
                  <div className="grid gap-2.5">
                    <RadioGroupList
                      onChange={(e: RadioChangeEvent) => {
                        setRadioVal(e.target.value);
                      }}
                      formInputClassName="!p-0"
                      value={radioVal}
                      view={"row"}
                      options={BILLEDOPTION}
                    />
                    {radioVal !== "leave_blank" ? (
                      <>
                        <Header
                          level={5}
                          className={`!text-sm !font-normal sm:h-[23px] !mb-0 !text-primary-900 ${
                            radioVal == "fixed" ? "" : "flex items-center"
                          }`}
                        >
                          {_t(
                            "Enter the percentage for each item that should be recorded on the Bill."
                          )}
                          {radioVal == "fixed" ? (
                            <InputField
                              fieldClassName="before:hidden"
                              applyBorder={true}
                              placeholder="0"
                              formInputClassName="!w-[62px] inline-block ml-2"
                              className="!border-[#DFE2E7] bg-[#EFF1F3] !rounded !text-13 !px-1.5 !py-0.5 !h-5 cf-input-right"
                              defaultValue={0}
                              suffix="%"
                              onInput={(e) => handleDecimalInput(e, 100)}
                              onChange={(e) => handleFixedIntChange(e.target)}
                            />
                          ) : (
                            ""
                          )}
                        </Header>

                        <div className="p-2 common-card overflow-hidden">
                          <div className="sm:max-h-[calc(100dvh-240px)] max-h-[calc(100dvh-285px)] overflow-y-auto hover-scroll">
                            <table className="generate-bill border-collapse w-full min-w-[550px]">
                              <thead className="sticky top-0 z-10">
                                <tr
                                  style={{
                                    backgroundColor: "#fafafa",
                                  }}
                                >
                                  <th className="p-2.5 font-semibold">
                                    {"% " + _t("To Bill")}
                                  </th>
                                  <th className="p-2.5 font-semibold text-left">
                                    {_t("Item Name")}
                                  </th>
                                  <th className="p-2.5 font-semibold text-right">
                                    {_t("Billed")}
                                  </th>
                                  <th className="p-2.5 font-semibold text-right">
                                    {_t("Remaining")}
                                  </th>
                                </tr>
                              </thead>
                              <tbody>
                                {generateBills.map((item) => {
                                  const itemId = item.item_id;
                                  const subject = item?.subject;

                                  const newValueBilled =
                                    Number(item?.total_sc_paid_bill_amt) / 100;
                                  const billed = formatter(
                                    newValueBilled.toFixed(2)
                                  ).value_with_symbol;

                                  const newValueRemaining =
                                    (Number(item?.total) -
                                      Number(item?.total_sc_paid_bill_amt)) /
                                    100;
                                  const remaining = formatter(
                                    newValueRemaining.toFixed(2)
                                  ).value_with_symbol;

                                  const remainingPer = formatter(
                                    (
                                      100 -
                                      Number(
                                        item?.total_sc_paid_bill_percentage
                                      )
                                    ).toFixed(2)
                                  ).value;
                                  const billedPer = formatter(
                                    Number(
                                      item?.total_sc_paid_bill_percentage
                                    ).toFixed(2)
                                  ).value;
                                  return (
                                    <tr
                                      className="h-[34px] hover:bg-[#22355814]"
                                      key={itemId}
                                    >
                                      <td className="px-2.5 w-[11%]">
                                        <InputField
                                          fieldClassName="before:hidden"
                                          applyBorder={true}
                                          placeholder="0"
                                          formInputClassName="!w-[62px] inline-block ml-2"
                                          className="!border-[#DFE2E7] bg-[#EFF1F3] !rounded !text-13 !px-1.5 !py-0.5 !h-5 cf-input-right"
                                          value={inputValues[itemId]}
                                          suffix="%"
                                          onInput={(e) =>
                                            handleDecimalInput(
                                              e,
                                              Number(remainingPer)
                                            )
                                          }
                                          onChange={(e) =>
                                            handleInputChange(
                                              itemId,
                                              e.target.value,
                                              Number(remainingPer)
                                            )
                                          }
                                        />
                                      </td>
                                      <td className="px-2.5 w-[40%] relative">
                                        <Tooltip title={subject}>
                                          <Typography className="table-tooltip-text absolute top-1/2 -translate-y-1/2">
                                            {subject}
                                          </Typography>
                                        </Tooltip>
                                      </td>
                                      <td className="px-2.5 w-[25%]">
                                        <div className="justify-end flex gap-1 items-center ">
                                          <Tooltip title={billed}>
                                            <Typography className="table-tooltip-text">
                                              {billed}
                                            </Typography>
                                          </Tooltip>
                                          <Typography className="table-tooltip-text !max-w-[60px]">
                                            {`(${billedPer}%)`}
                                          </Typography>
                                        </div>
                                      </td>
                                      <td className="px-2.5 w-[25%]">
                                        <div className="justify-end flex gap-1 items-center ">
                                          <Tooltip title={remaining}>
                                            <Typography className="table-tooltip-text">
                                              {remaining}
                                            </Typography>
                                          </Tooltip>
                                          <Typography className="table-tooltip-text !max-w-[60px]">
                                            {`(${remainingPer}%)`}
                                          </Typography>
                                        </div>
                                      </td>
                                    </tr>
                                  );
                                })}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </>
                    ) : (
                      <></>
                    )}
                  </div>
                )}
              </SidebarCardBorder>
            </div>
          </div>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
          <PrimaryButton
            htmlType="button"
            buttonText={_t("Generate a Bill")}
            disabled={isLoading || isFormLoading}
            isLoading={isFormLoading}
            onClick={handleGenerateBill}
          />
        </div>
      </div>
    </Drawer>
  );
};

export default Billed;
