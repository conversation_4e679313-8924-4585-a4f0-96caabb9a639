// import React, { useEffect, useMemo, useRef, useState } from "react";
// import { AgGridReact } from "ag-grid-react";
// import { GridApi } from "ag-grid-community";
// import debounce from "lodash/debounce";
// import isEmpty from "lodash/isEmpty";
// import { escapeHtmlEntities } from "~/helpers/helper";
// import { getProjectListApi } from "../../redux/action/proDashAction";

// const ProjectListGrid: React.FC = () => {
//   const gridApiRef = useRef<GridApi | null>(null);

//   // State for search and filter
//   const [search, setSearch] = useState<string>("");
//   const [filter, setFilter] = useState<IProjectListFilter>({});

//   // Build the datasource function
//   const getServerSideDatasource = (
//     searchValue: string,
//     filterValue: IProjectListFilter
//   ) => ({
//     getRows: async (params: any) => {
//       try {
//         const { startRow, endRow, sortModel } = params.request;
//         const pageSize = endRow - startRow;
//         const page = Math.floor(startRow / pageSize);

//         const order_by_name = sortModel?.[0]?.colId || undefined;
//         const order_by_dir = sortModel?.[0]?.sort || undefined;

//         const dataParams: IProjectListParmas = {
//           search: searchValue.trim()
//             ? escapeHtmlEntities(searchValue)
//             : undefined,
//           filter: !isEmpty(filterValue) ? filterValue : undefined,
//           limit: pageSize,
//           page,
//           order_by_name,
//           order_by_dir,
//           is_kanban: false,
//         };

//         const response = await getProjectListApi(dataParams);
//         const rows = response?.data || [];
//         const totalRows = response?.total || 0;

//         params.successCallback(rows, totalRows);
//       } catch (error) {
//         console.error("Error loading rows:", error);
//         params.failCallback();
//       }
//     },
//   });

//   // Debounce search for better UX
//   const debouncedSearch = useMemo(
//     () =>
//       debounce((newSearch: string, newFilter: IProjectListFilter) => {
//         if (gridApiRef.current) {
//           gridApiRef.current.setServerSideDatasource(
//             getServerSideDatasource(newSearch, newFilter)
//           );
//         }
//       }, 500),
//     []
//   );

//   // On search input change
//   const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     const newSearch = e.target.value;
//     setSearch(newSearch);
//     debouncedSearch(newSearch, filter);
//   };

//   // On filter change (replace this with your filter UI)
//   const handleFilterChange = (newFilter: IProjectListFilter) => {
//     setFilter(newFilter);
//     debouncedSearch(search, newFilter);
//   };

//   // AG Grid column definitions
//   const columnDefs = useMemo(
//     () => [
//       { headerName: "ID", field: "id", sortable: true, filter: false },
//       {
//         headerName: "Project Name",
//         field: "name",
//         sortable: true,
//         filter: false,
//       },
//       { headerName: "Status", field: "status", sortable: true, filter: false },
//       // add more columns...
//     ],
//     []
//   );

//   // Initial datasource on mount
//   useEffect(() => {
//     if (gridApiRef.current) {
//       gridApiRef.current.setServerSideDatasource(
//         getServerSideDatasource(search, filter)
//       );
//     }
//   }, []);

//   return (
//     <div className="ag-theme-alpine" style={{ height: "600px", width: "100%" }}>
//       <div className="flex items-center mb-2">
//         <input
//           type="text"
//           value={search}
//           onChange={handleSearchChange}
//           placeholder="Search projects..."
//           className="border px-2 py-1 rounded mr-2"
//         />
//         {/* Replace this with your filter UI */}
//         <button
//           onClick={() => handleFilterChange({ status: "active" })}
//           className="bg-blue-500 text-white px-3 py-1 rounded"
//         >
//           Active Filter
//         </button>
//       </div>

//       <AgGridReact
//         columnDefs={columnDefs}
//         rowModelType="serverSide"
//         serverSideStoreType="partial"
//         pagination
//         paginationPageSize={20}
//         cacheBlockSize={20} // match paginationPageSize
//         animateRows
//         onGridReady={(params) => {
//           gridApiRef.current = params.api;
//           params.api.setServerSideDatasource(
//             getServerSideDatasource(search, filter)
//           );
//         }}
//         onFilterChanged={() => {
//           // If AG Grid's own filter changes, reset to page 1
//           gridApiRef.current?.setServerSideDatasource(
//             getServerSideDatasource(search, filter)
//           );
//         }}
//         onSortChanged={() => {
//           gridApiRef.current?.refreshServerSideStore({ purge: true });
//         }}
//       />
//     </div>
//   );
// };

// export default ProjectListGrid;

// // import React from 'react'
// // import { getProjectListApi } from '../../redux/action/proDashAction';

// // interface ProjectListProps {
// //   setAddProjectOpen: (value: boolean) => void;
// //   search: string;
// // }
// // // i want to use ag grid server side row modal for api call and then set there is search and filter if they change start pagintion to 1 and then call api with new params
// // //  let dataParams: IProjectListParmas = {
// // //         search: escapeHtmlEntities(search),
// // //         filter: tempFil,
// // //         limit: length,
// // //         page: changeGridParams?.start
// // //           ? Math.floor(changeGridParams?.start / length)
// // //           : 0,
// // //         is_kanban: false,
// // //       };
// // //       if (search === "") {
// // //         delete dataParams.search;
// // //       }

// // //       if (isEmpty(tempFil)) {
// // //         delete dataParams.filter;
// // //       }
// // const ProjectList = ({ setAddProjectOpen, search }: ProjectListProps) => {

// //   const handleAPICall = ({
// //     page,
// //     search,
// //     filter
// //   }: {
// //     page: number;
// //     search: string;
// //     filter: any;
// //   }) => {
// //     // API call logic here
// //   }

// //   const handleFilterChange = async ({
// //     filter,
// //     search,
// //   }) => {
// //     const resData = (await getProjectListApi({
// //       page: 1,
// //       search,
// //       filter,
// //       limit: 20,
// //     })) as IProjectListApiRes;
// //     // API call logic here
// //   }
// //   return (
// //     <div>ProjectList</div>
// //   )
// // }

// // export default ProjectList
